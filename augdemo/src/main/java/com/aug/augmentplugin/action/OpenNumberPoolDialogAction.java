package com.aug.augmentplugin.action;

import com.aug.augmentplugin.ui.NumberPoolDialog;
import com.intellij.openapi.actionSystem.AnAction;
import com.intellij.openapi.actionSystem.AnActionEvent;
import com.intellij.openapi.actionSystem.CommonDataKeys;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.wm.StatusBar;
import com.intellij.openapi.wm.WindowManager;
import org.jetbrains.annotations.NotNull;

/**
 * <AUTHOR>
 */
public class OpenNumberPoolDialogAction extends AnAction {

    @Override
    public void actionPerformed(@NotNull AnActionEvent e) {
        try {
            Project project;
            if (e == null) {
                OpenNumberPoolDialogAction.reportNull(0);
            }
            if ((project = (Project)e.getData(CommonDataKeys.PROJECT)) == null) {
                System.out.println("No project available for NumberPool dialog");
                return;
            }

            System.out.println("Opening NumberPool dialog for project: " + project.getName());
            NumberPoolDialog dialog = new NumberPoolDialog(project);
            dialog.show();
            this.updateStatusBar(project);
            System.out.println("NumberPool dialog opened successfully");
        } catch (Exception ex) {
            System.err.println("Error opening NumberPool dialog: " + ex.getMessage());
            ex.printStackTrace();
        }
    }

    private void updateStatusBar(Project project) {
        if (project == null) return;
        if (project.isDisposed()) return;
        StatusBar statusBar = WindowManager.getInstance().getStatusBar(project);
        if (statusBar == null) return;
        statusBar.updateWidget("NumberPoolWidget");
    }

    @Override
    public void update(@NotNull AnActionEvent e) {
        if (e == null) {
            OpenNumberPoolDialogAction.reportNull(1);
        }
        Project project = (Project)e.getData(CommonDataKeys.PROJECT);
        e.getPresentation().setEnabled(project != null);
    }

    private static void reportNull(int n) {
        Object[] objectArray;
        Object[] objectArray2 = new Object[3];
        objectArray2[0] = "e";
        objectArray2[1] = "com/aug/augmentplugin/action/OpenNumberPoolDialogAction";
        switch (n) {
            default: {
                objectArray = objectArray2;
                objectArray2[2] = "actionPerformed";
                break;
            }
            case 1: {
                objectArray = objectArray2;
                objectArray2[2] = "update";
                break;
            }
        }
        throw new IllegalArgumentException(String.format("Argument for @NotNull parameter '%s' of %s.%s must not be null", objectArray[0], objectArray[1], objectArray[2]));
    }
}
