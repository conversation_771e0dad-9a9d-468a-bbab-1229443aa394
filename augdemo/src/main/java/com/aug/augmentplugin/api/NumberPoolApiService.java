package com.aug.augmentplugin.api;

import com.aug.augmentplugin.AugmentTokenManagerSettings;
import com.aug.augmentplugin.model.User;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.components.Service;
import com.intellij.util.io.HttpRequests;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.util.concurrent.CompletableFuture;
import javax.net.ssl.HttpsURLConnection;

@Service
public final class NumberPoolApiService {
//    private static final String API_URL = "https://deepl.micosoft.icu/api";
    private static final String API_URL = "http://localhost:8080/api";
    private static final String AGENT = "main";
    private final Gson gson = new Gson();
    private final AugmentTokenManagerSettings settings = AugmentTokenManagerSettings.getInstance();

    public static NumberPoolApiService getInstance() {
        return (NumberPoolApiService)ApplicationManager.getApplication().getService(NumberPoolApiService.class);
    }

    /**
     * 测试网络连接
     */
    public boolean testConnection() {
        try {
            System.out.println("Testing connection to: " + API_URL);
            String response = HttpRequests.request(API_URL + "/health")
                .connectTimeout(5000)
                .readTimeout(10000)
                .tuner(connection -> {
                    // 配置SSL连接以避免握手问题
                    if (connection instanceof HttpsURLConnection) {
                        HttpsURLConnection httpsConnection = (HttpsURLConnection) connection;
                        // 设置更宽松的SSL配置（仅用于开发环境）
                        httpsConnection.setHostnameVerifier((hostname, session) -> true);
                    }
                })
                .readString();
            System.out.println("Connection test successful: " + response);
            return true;
        } catch (Exception e) {
            System.err.println("Connection test failed: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    public CompletableFuture<User> cardLogin(String code) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                System.out.println("Starting card login with code: " + (code != null ? code.substring(0, Math.min(3, code.length())) + "..." : "null"));

                if (code == null || code.trim().isEmpty()) {
                    throw new IllegalArgumentException("激活码不能为空-----");
                }

                JsonObject requestBody = new JsonObject();
                requestBody.addProperty("card", code.trim());
                requestBody.addProperty("agent", AGENT);

                JsonObject responseJson = this.makePostRequestWithLogging("/users/card-login", requestBody, false);
                User user = this.gson.fromJson(responseJson.get("data"), User.class);
                System.out.println("Card login successful for user: " + (user != null ? user.toString() : "null"));
                return user;
            }
            catch (IOException e) {
                System.err.println("Network error during card login: " + e.getMessage());
                e.printStackTrace();
                throw new RuntimeException("网络连接失败，请检查网络设置: " + e.getMessage(), e);
            }
            catch (IllegalArgumentException e) {
                System.err.println("Invalid input for card login: " + e.getMessage());
                throw new RuntimeException(e.getMessage(), e);
            }
            catch (Exception e) {
                System.err.println("Unexpected error during card login: " + e.getMessage());
                e.printStackTrace();
                throw new RuntimeException("登录失败: " + e.getMessage(), e);
            }
        });
    }

    public CompletableFuture<User> whoami() {
        return CompletableFuture.supplyAsync(() -> {
            try {
                JsonObject responseJson = this.makeSimplePostRequest("/users/whoami", true);
                return this.gson.fromJson(responseJson.get("data"), User.class);
            }
            catch (IOException e) {
                e.printStackTrace();
                throw new RuntimeException("\u7f51\u7edc\u8bf7\u6c42\u5931\u8d25: " + e.getMessage(), e);
            }
            catch (Exception e) {
                e.printStackTrace();
                throw new RuntimeException("\u83b7\u53d6\u7528\u6237\u4fe1\u606f\u5931\u8d25: " + e.getMessage(), e);
            }
        });
    }

    public CompletableFuture<Void> logout() {
        return CompletableFuture.supplyAsync(() -> {
            try {
                this.makeSimplePostRequest("/users/logout", true);
                return null;
            }
            catch (IOException e) {
                e.printStackTrace();
                throw new RuntimeException("\u7f51\u7edc\u8bf7\u6c42\u5931\u8d25: " + e.getMessage(), e);
            }
            catch (Exception e) {
                e.printStackTrace();
                throw new RuntimeException("\u9000\u51fa\u767b\u5f55\u5931\u8d25: " + e.getMessage(), e);
            }
        });
    }

    private JsonObject makePostRequestWithLogging(String endpoint, JsonObject requestBody, boolean requireAuth) throws IOException, Exception {
        String requestJson = this.gson.toJson(requestBody);
        String fullUrl = API_URL + endpoint;
        System.out.println("=== API Request ===");
        System.out.println("URL: " + fullUrl);
        System.out.println("Request JSON: " + requestJson);
        String authToken = null;
        if (requireAuth && this.settings.isUserLoggedIn() && StringUtils.isNotBlank(authToken = this.settings.getUserToken())) {
            System.out.println("Auth Token: " + authToken.substring(0, Math.min(10, authToken.length())) + "...");
        }
        String finalAuthToken = authToken;
        String response = (String)HttpRequests.post((String)fullUrl, (String)"application/json").connect(request -> {
            if (StringUtils.isNotBlank(finalAuthToken)) {
                request.getConnection().setRequestProperty("X-Auth-Token", finalAuthToken);
            }
            request.write(requestJson);
            return request.readString();
        });
        System.out.println("Response JSON: " + response);
        System.out.println("===================");
        JsonObject responseJson = this.gson.fromJson(response, JsonObject.class);
        int code = responseJson.get("code").getAsInt();
        if (code <= 0) return responseJson;
        String msg = responseJson.get("msg").getAsString();
        throw new Exception(msg);
    }

    private JsonObject makeSimplePostRequest(String endpoint, boolean requireAuth) throws IOException, Exception {
        return this.makePostRequestWithLogging(endpoint, new JsonObject(), requireAuth);
    }
}
