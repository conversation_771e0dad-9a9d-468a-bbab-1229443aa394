package com.aug.augmentplugin;

import com.aug.augmentplugin.model.User;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.components.PersistentStateComponent;
import com.intellij.openapi.components.Service;
import com.intellij.openapi.components.State;
import com.intellij.openapi.components.Storage;
import com.intellij.util.xmlb.XmlSerializerUtil;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Service
@State(name="com.aug.augmentplugin.AugmentTokenManagerSettings", storages={@Storage(value="augmentTokenManagerSettings.xml")})
public final class AugmentTokenManagerSettings implements PersistentStateComponent<AugmentTokenManagerSettings> {
    private String sessionID = "";
    private String originalSessionID = "";
    private String userId = "";
    private String userToken = "";
    private boolean userVip = false;
    private long vipExpireAt = 0L;

    public static AugmentTokenManagerSettings getInstance() {
        return (AugmentTokenManagerSettings)ApplicationManager.getApplication().getService(AugmentTokenManagerSettings.class);
    }

    public String getSessionID() {
        return this.sessionID;
    }

    public void setSessionID(String sessionID) {
        this.sessionID = sessionID;
    }

    public String getOriginalSessionID() {
        return this.originalSessionID;
    }

    public void setOriginalSessionID(String originalSessionID) {
        this.originalSessionID = originalSessionID;
    }

    public String getUserId() {
        return this.userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUserToken() {
        return this.userToken;
    }

    public void setUserToken(String userToken) {
        this.userToken = userToken;
    }

    public boolean isUserVip() {
        return this.userVip;
    }

    public void setUserVip(boolean userVip) {
        this.userVip = userVip;
    }

    public long getVipExpireAt() {
        return this.vipExpireAt;
    }

    public void setVipExpireAt(long vipExpireAt) {
        this.vipExpireAt = vipExpireAt;
    }

    public boolean isUserLoggedIn() {
        return this.userId != null && !this.userId.isEmpty();
    }

    public void updateUserInfo(User user) {
        if (user == null) return;
        this.userId = user.getId();
        this.userToken = user.getToken();
        this.userVip = user.isVip();
        if (user.getVip() == null) return;
        this.vipExpireAt = user.getVip().expire_at;
    }

    public void clearUserData() {
        this.userId = "";
        this.userToken = "";
        this.userVip = false;
        this.vipExpireAt = 0L;
    }

    @Override
    @Nullable
    public AugmentTokenManagerSettings getState() {
        return this;
    }

    @Override
    public void loadState(@NotNull AugmentTokenManagerSettings state) {
        if (state == null) {
            AugmentTokenManagerSettings.reportNull(0);
        }
        XmlSerializerUtil.copyBean((Object)state, (Object)this);
    }

    private static void reportNull(int n) {
        throw new IllegalArgumentException(String.format("Argument for @NotNull parameter '%s' of %s.%s must not be null", "state", "com/aug/augmentplugin/AugmentTokenManagerSettings", "loadState"));
    }
}
