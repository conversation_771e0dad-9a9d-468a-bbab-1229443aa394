package com.aug.augmentplugin.startup;

import com.aug.augmentplugin.AugmentTokenManagerSettings;
import com.aug.augmentplugin.api.NumberPoolApiService;
import com.aug.augmentplugin.model.User;
import com.aug.augmentplugin.util.AugmentRequestUtils;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.startup.ProjectActivity;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

/**
 * 项目启动时初始化 Augment Request Session ID
 * 使用现代的 ProjectActivity API 替代已弃用的 StartupActivity
 * <AUTHOR>
 */
public final class AugmentRequestSessionIDInitializer implements ProjectActivity {
    private final AugmentTokenManagerSettings settings = AugmentTokenManagerSettings.getInstance();

    @Nullable
    @Override
    public Object execute(@NotNull Project project, @NotNull Continuation<? super Unit> continuation) {
        initializeSessionID(project);
        return Unit.INSTANCE;
    }

    private void initializeSessionID(@NotNull Project project) {
        String sessionID;
        String savedOriginalSessionID;
        String originalSessionID;
        if (StringUtils.isNotBlank(originalSessionID = AugmentRequestUtils.getSessionID())) {
            System.out.println("originalSessionID: " + originalSessionID);
            AugmentRequestUtils.originalSessionID = originalSessionID;
        }
        if (StringUtils.isNotBlank(savedOriginalSessionID = this.settings.getOriginalSessionID()) && StringUtils.isNotBlank(originalSessionID) && !originalSessionID.equals(savedOriginalSessionID)) {
            this.settings.setSessionID("");
            AugmentRequestUtils.sessionIDConflict = true;
            System.out.println("Check modify sessionID conflict");
            System.out.println("Current SessionID: " + originalSessionID);
            System.out.println("Saved Original SessionID: " + savedOriginalSessionID);
            return;
        }
        if (StringUtils.isBlank(savedOriginalSessionID) && StringUtils.isNotBlank(originalSessionID)) {
            this.settings.setOriginalSessionID(originalSessionID);
        }
        if (StringUtils.isNotBlank(sessionID = this.settings.getSessionID())) {
            System.out.println("sessionID: " + sessionID);
            AugmentRequestUtils.setSessionID((String)sessionID);
        }
        this.refreshUserInfoOnStartup();
    }

    private void refreshUserInfoOnStartup() {
        if (!this.settings.isUserLoggedIn()) {
            // 如果用户未登录，仅记录日志，不自动显示登录对话框
            // 避免每次启动都弹出对话框影响用户体验
            System.out.println("User not logged in. Login dialog can be opened manually from Tools menu.");
            return;
        }
        System.out.println("User is logged in, refreshing user info on startup...");
        ApplicationManager.getApplication().executeOnPooledThread(() -> {
            try {
                NumberPoolApiService apiService = NumberPoolApiService.getInstance();

                // 首先测试网络连接
                if (!apiService.testConnection()) {
                    System.err.println("Network connection test failed during startup. Skipping user info refresh.");
                    return;
                }

                try {
                    User user = (User)apiService.whoami().get();
                    ApplicationManager.getApplication().invokeLater(() -> {
                        this.settings.updateUserInfo(user);
                        System.out.println("Startup user info updated: " + String.valueOf(user));
                    });
                }
                catch (Exception ex) {
                    System.err.println("Failed to refresh user info on startup: " + ex.getMessage());
                    // 如果刷新失败，仅记录错误，不显示对话框
                    // 检查是否是SSL相关错误
                    if (ex.getCause() instanceof javax.net.ssl.SSLHandshakeException) {
                        System.err.println("SSL handshake error detected. This may be due to network configuration or certificate issues.");
                    }
                }
            }
            catch (Exception ex) {
                System.err.println("Failed to refresh user info on startup: " + ex.getMessage());
                ex.printStackTrace();
            }
        });
    }
}
