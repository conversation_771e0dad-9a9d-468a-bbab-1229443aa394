package com.aug.augmentplugin.model;

public class User {
    private String id;
    private String token;
    private VipInfo vip;

    public User() {
    }

    public User(String id, String token, VipInfo vip) {
        this.id = id;
        this.token = token;
        this.vip = vip;
    }

    public String getId() {
        return this.id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getToken() {
        return this.token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public VipInfo getVip() {
        return this.vip;
    }

    public void setVip(VipInfo vip) {
        this.vip = vip;
    }

    public boolean isVip() {
        if (this.vip != null) return this.vip.expire_at > System.currentTimeMillis();
        return false;
    }

    @Override
    public String toString() {
        return "User{id='" + this.id + "', token='" + this.token + "', vip=" + String.valueOf(this.vip) + "}";
    }
}
