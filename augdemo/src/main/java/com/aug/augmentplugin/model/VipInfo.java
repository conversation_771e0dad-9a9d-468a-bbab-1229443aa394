package com.aug.augmentplugin.model;

public class VipInfo {
    public int id;
    public long expire_at;
    public float day_score;
    public long refresh_at;
    public int power;
    public String product;
    public int score;
    public int score_used;

    public VipInfo() {
    }

    public VipInfo(int id, long expire_at, float day_score, long refresh_at, int power, String product, int score, int score_used) {
        this.id = id;
        this.expire_at = expire_at;
        this.day_score = day_score;
        this.refresh_at = refresh_at;
        this.power = power;
        this.product = product;
        this.score = score;
        this.score_used = score_used;
    }

    @Override
    public String toString() {
        return "VipInfo{id=" + this.id + ", expire_at=" + this.expire_at + ", day_score=" + this.day_score + ", refresh_at=" + this.refresh_at + ", power=" + this.power + ", product='" + this.product + "', score=" + this.score + ", score_used=" + this.score_used + "}";
    }
}
