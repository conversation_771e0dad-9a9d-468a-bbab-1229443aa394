package com.aug.augmentplugin;

import com.aug.augmentplugin.util.AugmentRequestUtils;
import com.intellij.ui.components.JBLabel;
import com.intellij.ui.components.JBTextField;
import com.intellij.util.ui.FormBuilder;
import com.intellij.util.ui.JBUI;
import org.apache.commons.lang3.StringUtils;

import javax.swing.*;
import javax.swing.border.Border;
import java.awt.*;
import java.util.Objects;
import java.util.UUID;

/**
 * <AUTHOR>
 */
public class AugmentTokenManagerConfigPanel extends JPanel {
    private static final String DIALOG_TITLE_SUCCESS = "\u64cd\u4f5c\u6210\u529f";
    private static final String DIALOG_TITLE_FAILURE = "\u64cd\u4f5c\u5931\u8d25";
    private final JBTextField tenantUrlField;
    private final JBTextField authTokenField;
    private final JBTextField sessionIDField;
    private final JButton resetSessionIDButton;
    private final JButton restoreSessionIDButton;
    private final JBLabel conflictStatusLabel;
    private final CredentialManager credentialManager = new CredentialManager();
    private final AugmentTokenManagerSettings augmentTokenManagerSettings = AugmentTokenManagerSettings.getInstance();
    private String currentSessionID;

    public AugmentTokenManagerConfigPanel() {
        super(new BorderLayout());
        this.tenantUrlField = this.createTextField(this.credentialManager.getTenantUrl(), "\u8f93\u5165\u60a8\u7684 Augment \u8d26\u53f7\u6216\u79df\u6237URL", "\u8d26\u53f7/\u79df\u6237URL");
        this.authTokenField = this.createTextField(this.credentialManager.getAuthToken(), "\u8f93\u5165\u60a8\u7684 Augment \u8bbf\u95ee\u4ee4\u724c", "\u8bbf\u95ee\u4ee4\u724c");
        this.sessionIDField = this.createTextField("", "\u67e5\u770b\u6216\u91cd\u7f6e Augment SessionID", "SessionID");
        this.resetSessionIDButton = new JButton("\u751f\u6210");
        this.resetSessionIDButton.addActionListener(e -> this.generateSessionID());
        this.restoreSessionIDButton = new JButton("\u8fd8\u539f");
        this.restoreSessionIDButton.addActionListener(e -> this.restoreSessionID());
        this.conflictStatusLabel = new JBLabel();
        this.conflictStatusLabel.setForeground(new Color(255, 0, 0));
        this.buildUI();
        this.refreshSessionID();
        this.updateUIState();
    }

    private JBTextField createTextField(String text, String tooltip, String emptyText) {
        JBTextField field = new JBTextField(text);
        field.setToolTipText(tooltip);
        field.getEmptyText().setText(emptyText);
        return field;
    }

    private void buildUI() {
        JPanel sessionIDPanel = new JPanel(new BorderLayout(5, 0));
        sessionIDPanel.add((Component)this.sessionIDField, "Center");
        JPanel sessionButtonsPanel = new JPanel(new FlowLayout(0, 5, 0));
        sessionButtonsPanel.add(this.resetSessionIDButton);
        sessionButtonsPanel.add(this.restoreSessionIDButton);
        sessionIDPanel.add((Component)sessionButtonsPanel, "East");
        JBLabel infoLabel = this.createBoldLabel("\u8bf7\u8f93\u5165\u60a8\u7684 Augment OAuth \u51ed\u8bc1\uff1a", 0, 0, 10, 0);
        JBLabel sessionIDLabel = this.createBoldLabel("SessionID \u7ba1\u7406\uff1a", 20, 0, 10, 0);
        JPanel mainPanel = FormBuilder.createFormBuilder().addComponent((JComponent)sessionIDLabel).addLabeledComponent("SessionID:", (JComponent)sessionIDPanel, 5, true).addComponent((JComponent)this.conflictStatusLabel, 5).addComponent((JComponent)infoLabel).addLabeledComponent("\u8d26\u53f7/\u79df\u6237URL:", (JComponent)this.tenantUrlField, 5, true).addLabeledComponent("\u8bbf\u95ee\u4ee4\u724c:", (JComponent)this.authTokenField, 5, true).addComponentFillVertically((JComponent)new JPanel(), 0).getPanel();
        mainPanel.setBorder(JBUI.Borders.empty((int)10));
        this.add((Component)mainPanel, "Center");
    }

    private JBLabel createBoldLabel(String text, int top, int left, int bottom, int right) {
        JBLabel label = new JBLabel(text);
        Font font = label.getFont();
        label.setFont(font.deriveFont(1, font.getSize() + 2));
        label.setBorder((Border)JBUI.Borders.empty((int)top, (int)left, (int)bottom, (int)right));
        return label;
    }

    private void showMessage(String message, String title, int messageType) {
        System.out.println(title + ": " + title);
        JOptionPane.showMessageDialog(this, message, title, messageType);
    }

    private void refreshSessionID() {
        this.currentSessionID = AugmentRequestUtils.getSessionID();
        this.sessionIDField.setText((String)Objects.requireNonNullElse((Object)this.currentSessionID, (Object)""));
    }

    private void updateUIState() {
        boolean hasConflict = this.isSessionIDConflict();
        this.resetSessionIDButton.setEnabled(!hasConflict);
        this.restoreSessionIDButton.setEnabled(!hasConflict);
        this.sessionIDField.setEditable(!hasConflict);
        if (hasConflict) {
            this.conflictStatusLabel.setText("\u26a0\ufe0f \u68c0\u6d4b\u5230\u5176\u4ed6\u7a0b\u5e8f\u5df2\u4fee\u6539 SessionID\uff0c\u76f8\u5173\u529f\u80fd\u5df2\u7981\u7528\u4ee5\u907f\u514d\u51b2\u7a81");
            this.conflictStatusLabel.setToolTipText("\u5176\u4ed6\u7a0b\u5e8f\u5df2\u7ecf\u4fee\u6539\u4e86 SessionID\uff0c\u4e3a\u4e86\u907f\u514d\u51b2\u7a81\uff0cSessionID \u76f8\u5173\u529f\u80fd\u5df2\u88ab\u7981\u7528\u3002");
        } else {
            this.conflictStatusLabel.setText("");
            this.conflictStatusLabel.setToolTipText(null);
        }
        if (hasConflict) {
            String disabledTooltip = "\u529f\u80fd\u5df2\u7981\u7528\uff1a\u68c0\u6d4b\u5230\u5176\u4ed6\u7a0b\u5e8f\u5df2\u4fee\u6539 SessionID";
            this.resetSessionIDButton.setToolTipText(disabledTooltip);
            this.restoreSessionIDButton.setToolTipText(disabledTooltip);
            this.sessionIDField.setToolTipText(disabledTooltip);
        } else {
            this.resetSessionIDButton.setToolTipText("\u751f\u6210\u65b0\u7684\u968f\u673a SessionID");
            this.restoreSessionIDButton.setToolTipText("\u8fd8\u539f\u5230\u539f\u59cb SessionID");
            this.sessionIDField.setToolTipText("\u67e5\u770b\u6216\u91cd\u7f6e Augment SessionID");
        }
    }

    private void generateSessionID() {
        String newSessionID = UUID.randomUUID().toString();
        this.sessionIDField.setText(newSessionID);
    }

    private void restoreSessionID() {
        this.sessionIDField.setText((String)Objects.requireNonNullElse((Object)AugmentRequestUtils.originalSessionID, (Object)""));
    }

    public JComponent getPreferredFocusedComponent() {
        return this.tenantUrlField;
    }

    public boolean isModified() {
        String currentTenantUrl = this.credentialManager.getTenantUrl();
        String currentAuthToken = this.credentialManager.getAuthToken();
        String newSessionID = this.sessionIDField.getText();
        String newTenantUrl = this.tenantUrlField.getText();
        String newAuthToken = this.authTokenField.getText();
        boolean sessionIDModified = !Objects.equals(this.currentSessionID, newSessionID);
        boolean credentialsModified = !currentTenantUrl.equals(newTenantUrl) || !currentAuthToken.equals(newAuthToken);
        return credentialsModified || sessionIDModified;
    }

    public void apply() {
        System.out.println("Applying New Settings");
        this.applySessionID();
        this.applyCredentials();
    }

    private void applySessionID() {
        if (this.isSessionIDConflict()) {
            this.showMessage("\u68c0\u6d4b\u5230 SessionID \u51b2\u7a81\uff0c\u65e0\u6cd5\u4fee\u6539\u3002\u5176\u4ed6\u7a0b\u5e8f\u5df2\u7ecf\u4fee\u6539\u4e86 SessionID\u3002", DIALOG_TITLE_FAILURE, 2);
            return;
        }
        String newSessionID = this.sessionIDField.getText().trim();
        try {
            boolean success;
            if (Objects.equals(this.currentSessionID, newSessionID)) return;
            if (newSessionID.isEmpty()) return;
            if (StringUtils.isNotBlank(this.credentialManager.getAuthToken())) {
                this.credentialManager.reflectionClear();
            }
            if (success = AugmentRequestUtils.setSessionID((String)newSessionID)) {
                this.augmentTokenManagerSettings.setSessionID(newSessionID);
                this.currentSessionID = newSessionID;
                System.out.println("Successfully Applied SessionID: " + newSessionID);
            } else {
                this.showMessage("\u65e0\u6cd5\u4fee\u6539 SessionID\uff0c\u8bf7\u786e\u8ba4 Augment \u63d2\u4ef6\u5df2\u5b89\u88c5\u5e76\u6b63\u5e38\u8fd0\u884c\u3002", DIALOG_TITLE_FAILURE, 2);
            }
        }
        catch (Exception e) {
            System.out.println("Failed to Apply SessionID: " + e.getMessage());
            this.showMessage("\u4fdd\u5b58 SessionID \u5931\u8d25: " + e.getMessage(), DIALOG_TITLE_FAILURE, 0);
        }
    }

    private void applyCredentials() {
        String tenantUrl = this.tenantUrlField.getText();
        String authToken = this.authTokenField.getText();
        try {
            String currentTenantUrl = this.credentialManager.getTenantUrl();
            String currentAuthToken = this.credentialManager.getAuthToken();
            if (currentTenantUrl.equals(tenantUrl)) {
                if (currentAuthToken.equals(authToken)) return;
            }
            this.credentialManager.saveCredentials(tenantUrl, authToken);
            this.credentialManager.reflectionSaveCredentials(tenantUrl, authToken);
            System.out.println("Successfully Applied Credential Settings");
        }
        catch (Exception e) {
            System.out.println("Failed to Apply Credentials: " + e.getMessage());
            this.showMessage("\u4fdd\u5b58\u51ed\u8bc1\u5931\u8d25: " + e.getMessage(), DIALOG_TITLE_FAILURE, 0);
        }
    }

    public void reset() {
        this.tenantUrlField.setText(this.credentialManager.getTenantUrl());
        this.authTokenField.setText(this.credentialManager.getAuthToken());
        this.refreshSessionID();
        this.updateUIState();
    }

    public boolean isSessionIDConflict() {
        return AugmentRequestUtils.sessionIDConflict;
    }
}
