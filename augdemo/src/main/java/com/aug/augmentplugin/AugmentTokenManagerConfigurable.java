package com.aug.augmentplugin;

import com.intellij.openapi.options.Configurable;
import com.intellij.openapi.util.NlsContexts;
import org.jetbrains.annotations.Nullable;

import javax.swing.*;

/**
 * <AUTHOR>
 */
public class AugmentTokenManagerConfigurable implements Configurable {
    private AugmentTokenManagerConfigPanel configPanel;

    @Override
    @NlsContexts.ConfigurableName
    public String getDisplayName() {
        return "Augment Token Manager";
    }

    @Override
    public JComponent getPreferredFocusedComponent() {
        return this.configPanel != null ? this.configPanel.getPreferredFocusedComponent() : null;
    }

    @Override
    @Nullable
    public JComponent createComponent() {
        this.configPanel = new AugmentTokenManagerConfigPanel();
        return this.configPanel;
    }

    @Override
    public boolean isModified() {
        return this.configPanel != null && this.configPanel.isModified();
    }

    @Override
    public void apply() {
        if (this.configPanel == null) return;
        this.configPanel.apply();
    }

    @Override
    public void reset() {
        if (this.configPanel == null) return;
        this.configPanel.reset();
    }

    @Override
    public void disposeUIResources() {
        this.configPanel = null;
    }
}
