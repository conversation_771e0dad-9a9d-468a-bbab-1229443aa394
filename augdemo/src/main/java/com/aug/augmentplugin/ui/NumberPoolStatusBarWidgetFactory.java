package com.aug.augmentplugin.ui;

import com.intellij.openapi.project.Project;
import com.intellij.openapi.wm.StatusBar;
import com.intellij.openapi.wm.StatusBarWidget;
import com.intellij.openapi.wm.StatusBarWidgetFactory;
import org.jetbrains.annotations.Nls;
import org.jetbrains.annotations.NotNull;

public class NumberPoolStatusBarWidgetFactory implements StatusBarWidgetFactory {
    private static final String WIDGET_ID = "NumberPoolWidget";

    @Override
    @NotNull
    public String getId() {
        return WIDGET_ID;
    }

    @Override
    @Nls
    @NotNull
    public String getDisplayName() {
        return "Number Pool";
    }

    @Override
    public boolean isAvailable(@NotNull Project project) {
        if (project != null) return true;
        NumberPoolStatusBarWidgetFactory.reportNull(0);
        return true;
    }

    @Override
    @NotNull
    public StatusBarWidget createWidget(@NotNull Project project) {
        if (project != null) return new NumberPoolStatusBarWidget(project);
        NumberPoolStatusBarWidgetFactory.reportNull(1);
        return new NumberPoolStatusBarWidget(project);
    }

    @Override
    public void disposeWidget(@NotNull StatusBarWidget widget) {
        if (widget == null) {
            NumberPoolStatusBarWidgetFactory.reportNull(2);
        }
        widget.dispose();
    }

    @Override
    public boolean canBeEnabledOn(@NotNull StatusBar statusBar) {
        if (statusBar != null) return true;
        NumberPoolStatusBarWidgetFactory.reportNull(3);
        return true;
    }

    private static void reportNull(int n) {
        Object[] objectArray;
        Object[] objectArray2;
        Object[] objectArray3 = new Object[3];
        switch (n) {
            default: {
                objectArray2 = objectArray3;
                objectArray3[0] = "project";
                break;
            }
            case 2: {
                objectArray2 = objectArray3;
                objectArray3[0] = "widget";
                break;
            }
            case 3: {
                objectArray2 = objectArray3;
                objectArray3[0] = "statusBar";
                break;
            }
        }
        objectArray2[1] = "com/aug/augmentplugin/ui/NumberPoolStatusBarWidgetFactory";
        switch (n) {
            default: {
                objectArray = objectArray2;
                objectArray2[2] = "isAvailable";
                break;
            }
            case 1: {
                objectArray = objectArray2;
                objectArray2[2] = "createWidget";
                break;
            }
            case 2: {
                objectArray = objectArray2;
                objectArray2[2] = "disposeWidget";
                break;
            }
            case 3: {
                objectArray = objectArray2;
                objectArray2[2] = "canBeEnabledOn";
                break;
            }
        }
        throw new IllegalArgumentException(String.format("Argument for @NotNull parameter '%s' of %s.%s must not be null", objectArray[0], objectArray[1], objectArray[2]));
    }
}
