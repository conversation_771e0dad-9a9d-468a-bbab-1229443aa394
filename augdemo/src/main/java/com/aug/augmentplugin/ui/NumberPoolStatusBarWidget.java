package com.aug.augmentplugin.ui;

import com.aug.augmentplugin.AugmentTokenManagerSettings;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.wm.StatusBar;
import com.intellij.openapi.wm.StatusBarWidget;
import com.intellij.openapi.wm.WindowManager;
import com.intellij.util.Consumer;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.awt.event.MouseEvent;

public class NumberPoolStatusBarWidget implements StatusBarWidget, StatusBarWidget.TextPresentation {
    private static final String WIDGET_ID = "NumberPoolWidget";
    private final Project project;
    private final AugmentTokenManagerSettings settings;

    public NumberPoolStatusBarWidget(@NotNull Project project) {
        if (project == null) {
            NumberPoolStatusBarWidget.reportNull(0);
        }
        this.project = project;
        this.settings = AugmentTokenManagerSettings.getInstance();
    }

    @Override
    @NotNull
    public String ID() {
        return WIDGET_ID;
    }

    @Override
    @Nullable
    public StatusBarWidget.WidgetPresentation getPresentation() {
        return this;
    }

    @Override
    public void install(@NotNull StatusBar statusBar) {
        if (statusBar != null) return;
        NumberPoolStatusBarWidget.reportNull(1);
    }

    @Override
    public void dispose() {
    }

    @Override
    @NotNull
    public String getText() {
        if (!this.settings.isUserLoggedIn()) return "\u6fc0\u6d3b";
        String userId = this.settings.getUserId();
        String string = "\u6fc0\u6d3b: " + userId;
        if (string != null) return string;
        NumberPoolStatusBarWidget.reportNull(2);
        return string;
    }

    @Override
    public float getAlignment() {
        return 0.0f;
    }

    @Override
    @Nullable
    public String getTooltipText() {
        long remainingDays;
        if (!this.settings.isUserLoggedIn()) return "\u6fc0\u6d3b\u7ba1\u7406 - \u70b9\u51fb\u767b\u5f55";
        String userId = this.settings.getUserId();
        Object vipStatus = "\u4f1a\u5458\u5df2\u8fc7\u671f";
        if (!this.settings.isUserVip()) return "\u6fc0\u6d3b\u7ba1\u7406 - \u7528\u6237: " + userId + "\n\u4f1a\u5458\u72b6\u6001: " + (String)vipStatus + "\n\u70b9\u51fb\u6253\u5f00\u7ba1\u7406\u754c\u9762";
        long vipExpireAt = this.settings.getVipExpireAt();
        vipStatus = vipExpireAt > 0L ? ((remainingDays = (vipExpireAt - System.currentTimeMillis()) / 86400000L) > 0L ? "VIP\u7528\u6237 (\u5269\u4f59" + remainingDays + "\u5929)" : "VIP\u7528\u6237 (\u4eca\u65e5\u5230\u671f)") : "VIP\u7528\u6237";
        return "\u6fc0\u6d3b\u7ba1\u7406 - \u7528\u6237: " + userId + "\n\u4f1a\u5458\u72b6\u6001: " + (String)vipStatus + "\n\u70b9\u51fb\u6253\u5f00\u7ba1\u7406\u754c\u9762";
    }

    @Override
    @Nullable
    public Consumer<MouseEvent> getClickConsumer() {
        return mouseEvent -> {
            NumberPoolDialog dialog = new NumberPoolDialog(this.project);
            dialog.show();
            this.updateStatusBar();
        };
    }

    private void updateStatusBar() {
        if (this.project == null) return;
        if (this.project.isDisposed()) return;
        StatusBar statusBar = WindowManager.getInstance().getStatusBar(this.project);
        if (statusBar == null) return;
        statusBar.updateWidget(WIDGET_ID);
    }

    private static void reportNull(int n) {
        Object[] objectArray;
        Object[] objectArray2;
        Object[] objectArray3 = new Object[switch (n) {
            default -> 3;
            case 2 -> 2;
        }];
        switch (n) {
            default: {
                objectArray2 = objectArray3;
                objectArray3[0] = "project";
                break;
            }
            case 1: {
                objectArray2 = objectArray3;
                objectArray3[0] = "statusBar";
                break;
            }
            case 2: {
                objectArray2 = objectArray3;
                objectArray3[0] = "com/aug/augmentplugin/ui/NumberPoolStatusBarWidget";
                break;
            }
        }
        switch (n) {
            default: {
                objectArray = objectArray2;
                objectArray2[1] = "com/aug/augmentplugin/ui/NumberPoolStatusBarWidget";
                break;
            }
            case 2: {
                objectArray = objectArray2;
                objectArray2[1] = "getText";
                break;
            }
        }
        switch (n) {
            default: {
                objectArray = objectArray;
                objectArray[2] = "<init>";
                break;
            }
            case 1: {
                objectArray = objectArray;
                objectArray[2] = "install";
                break;
            }
            case 2: {
                break;
            }
        }
        String formatString = switch (n) {
            default -> "Argument for @NotNull parameter '%s' of %s.%s must not be null";
            case 2 -> "Argument for @NotNull parameter '%s' of %s.%s must not be null";
        };
        String string = switch (n) {
            case 2 -> String.format(formatString, objectArray[0], objectArray[1]);
            default -> String.format(formatString, objectArray[0], objectArray[1], objectArray[2]);
        };
        throw switch (n) {
            default -> new IllegalArgumentException(string);
            case 2 -> new IllegalStateException(string);
        };
    }
}
