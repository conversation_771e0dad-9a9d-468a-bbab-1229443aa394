package com.aug.augmentplugin.ui;

import com.aug.augmentplugin.AugmentTokenManagerSettings;
import com.aug.augmentplugin.CredentialManager;
import com.aug.augmentplugin.api.NumberPoolApiService;
import com.intellij.openapi.progress.ProgressManager;
import com.intellij.openapi.progress.Task;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.ui.DialogWrapper;
import com.intellij.openapi.ui.Messages;
import com.intellij.openapi.wm.StatusBar;
import com.intellij.openapi.wm.WindowManager;
import com.intellij.ui.components.JBLabel;
import com.intellij.ui.components.JBTextField;
import com.intellij.util.ui.FormBuilder;
import com.intellij.util.ui.JBUI;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import javax.swing.*;
import java.awt.*;
import java.awt.datatransfer.StringSelection;
import java.awt.event.ActionEvent;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * <AUTHOR>
 */
public class NumberPoolDialog extends DialogWrapper {
    private final AugmentTokenManagerSettings settings;
    private final NumberPoolApiService apiService;
    private final Project project;
    private JBTextField activationCodeField;
    private JButton loginButton;
    private JPanel loginPanel;
    private JBLabel userIdLabel;
    private JBLabel vipExpiryLabel;
    private JButton copyUserIdButton;
    private JButton logoutButton;
    private JPanel userInfoPanel;
    private JPanel mainPanel;

    public NumberPoolDialog(Project project) {
        super(project, true);
        this.project = project;
        this.settings = AugmentTokenManagerSettings.getInstance();
        this.apiService = NumberPoolApiService.getInstance();
        this.setTitle("\u6fc0\u6d3b\u7ba1\u7406");
        this.setResizable(true);
        this.init();
        this.updateUI();
    }

    @Override
    @Nullable
    protected JComponent createCenterPanel() {
        this.mainPanel = new JPanel(new CardLayout());
        this.createLoginPanel();
        this.createUserInfoPanel();
        this.mainPanel.add((Component)this.loginPanel, "LOGIN");
        this.mainPanel.add((Component)this.userInfoPanel, "USER_INFO");
        return this.mainPanel;
    }

    private void createLoginPanel() {
        this.activationCodeField = new JBTextField();
        this.activationCodeField.getEmptyText().setText("\u8bf7\u8f93\u5165\u6fc0\u6d3b\u7801");
        this.loginButton = new JButton("\u767b\u5f55");
        this.loginButton.addActionListener(this::handleLogin);
        JPanel buttonPanel = new JPanel(new FlowLayout(1));
        buttonPanel.add(this.loginButton);
        this.loginPanel = FormBuilder.createFormBuilder().addLabeledComponent("\u6fc0\u6d3b\u7801:", (JComponent)this.activationCodeField, 10, true).addComponent((JComponent)buttonPanel, 10).getPanel();
        this.loginPanel.setBorder(JBUI.Borders.empty((int)20));
    }

    private void createUserInfoPanel() {
        this.userIdLabel = new JBLabel();
        this.userIdLabel.setFont(this.userIdLabel.getFont().deriveFont(1, 14.0f));
        this.vipExpiryLabel = new JBLabel();
        this.vipExpiryLabel.setFont(this.vipExpiryLabel.getFont().deriveFont(0, 12.0f));
        this.copyUserIdButton = new JButton("\u590d\u5236ID");
        this.copyUserIdButton.addActionListener(this::handleCopyUserId);
        this.logoutButton = new JButton("\u9000\u51fa\u767b\u5f55");
        this.logoutButton.addActionListener(this::handleLogout);
        JPanel userInfoWithButtonsPanel = new JPanel(new FlowLayout(0, 0, 0));
        userInfoWithButtonsPanel.add((Component)this.userIdLabel);
        userInfoWithButtonsPanel.add(Box.createHorizontalStrut(10));
        userInfoWithButtonsPanel.add(this.copyUserIdButton);
        userInfoWithButtonsPanel.add(Box.createHorizontalStrut(5));
        userInfoWithButtonsPanel.add(this.logoutButton);
        this.userInfoPanel = FormBuilder.createFormBuilder().addLabeledComponent("\u7528\u6237:", (JComponent)userInfoWithButtonsPanel, 5, true).addLabeledComponent("\u4f1a\u5458:", (JComponent)this.vipExpiryLabel, 5, true).getPanel();
        this.userInfoPanel.setBorder(JBUI.Borders.empty((int)20));
    }

    private void updateUI() {
        CardLayout cardLayout = (CardLayout)this.mainPanel.getLayout();
        if (this.settings.isUserLoggedIn()) {
            String userId = this.settings.getUserId();
            this.userIdLabel.setText(userId);
            this.updateVipExpiryDisplay();
            cardLayout.show(this.mainPanel, "USER_INFO");
        } else {
            cardLayout.show(this.mainPanel, "LOGIN");
        }
        this.pack();
    }

    private void updateVipExpiryDisplay() {
        long vipExpireAt = this.settings.getVipExpireAt();
        long currentTime = System.currentTimeMillis();
        if (this.settings.isUserVip() && vipExpireAt > 0L) {
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");
            String expireDate = dateFormat.format(new Date(vipExpireAt));
            if (vipExpireAt > currentTime) {
                long remainingDays = (vipExpireAt - currentTime) / 86400000L;
                if (remainingDays > 7L) {
                    this.vipExpiryLabel.setText("VIP\u7528\u6237 (\u5230\u671f: " + expireDate + ")");
                    this.vipExpiryLabel.setForeground(new Color(0, 128, 0));
                } else if (remainingDays > 0L) {
                    this.vipExpiryLabel.setText("VIP\u7528\u6237 (\u5269\u4f59" + remainingDays + "\u5929, \u5230\u671f: " + expireDate + ")");
                    this.vipExpiryLabel.setForeground(Color.ORANGE);
                } else {
                    this.vipExpiryLabel.setText("VIP\u7528\u6237 (\u4eca\u65e5\u5230\u671f: " + expireDate + ")");
                    this.vipExpiryLabel.setForeground(Color.RED);
                }
            } else {
                this.vipExpiryLabel.setText("VIP\u5df2\u8fc7\u671f (\u8fc7\u671f\u65f6\u95f4: " + expireDate + ")");
                this.vipExpiryLabel.setForeground(Color.RED);
            }
        } else if (this.settings.isUserVip()) {
            this.vipExpiryLabel.setText("VIP\u7528\u6237 (\u6709\u6548)");
            this.vipExpiryLabel.setForeground(new Color(0, 128, 0));
        } else {
            this.vipExpiryLabel.setText("\u4f1a\u5458\u5df2\u8fc7\u671f");
            this.vipExpiryLabel.setForeground(Color.GRAY);
        }
    }

    private void handleLogin(ActionEvent e) {
        String code = this.activationCodeField.getText().trim();
        if (code.isEmpty()) {
            Messages.showErrorDialog((String)"\u8bf7\u8f93\u5165\u6fc0\u6d3b\u7801", (String)"\u9519\u8bef");
            return;
        }
        this.loginButton.setEnabled(false);
        this.loginButton.setText("\u767b\u5f55\u4e2d...");
        ProgressManager.getInstance().run(new Task.Backgroundable(this.project, "登录中...", false) {
            @Override
            public void run(com.intellij.openapi.progress.ProgressIndicator indicator) {
                try {
                    indicator.setText("检查网络连接...");
                    NumberPoolApiService apiService = NumberPoolApiService.getInstance();

                    // 首先测试网络连接
                    if (!apiService.testConnection()) {
                        javax.swing.SwingUtilities.invokeLater(() -> {
                            loginButton.setEnabled(true);
                            loginButton.setText("登录");
                            Messages.showErrorDialog("网络连接失败，请检查网络设置", "网络错误");
                        });
                        return;
                    }

                    indicator.setText("正在登录...");
                    // 调用实际的登录API
                    com.aug.augmentplugin.model.User user = apiService.cardLogin(code).get();

                    // 在EDT线程中更新UI
                    javax.swing.SwingUtilities.invokeLater(() -> {
                        loginButton.setEnabled(true);
                        loginButton.setText("登录");
                        if (user != null) {
                            settings.updateUserInfo(user);
                            Messages.showInfoMessage("登录成功！用户: " + user.getId().toString(), "登录成功");
                            updateUI(); // 更新UI显示
                        } else {
                            Messages.showErrorDialog("登录失败：服务器返回空用户信息", "登录失败");
                        }
                    });
                } catch (Exception e) {
                    javax.swing.SwingUtilities.invokeLater(() -> {
                        loginButton.setEnabled(true);
                        loginButton.setText("登录");
                        String errorMsg = e.getMessage();
                        if (errorMsg == null || errorMsg.isEmpty()) {
                            errorMsg = "未知错误";
                        }
                        Messages.showErrorDialog("登录失败: " + errorMsg, "登录错误");
                    });
                }
            }
        });
    }

    private void handleCopyUserId(ActionEvent e) {
        String userId = this.settings.getUserId();
        if (userId == null) return;
        if (userId.isEmpty()) return;
        StringSelection selection = new StringSelection(userId);
        Toolkit.getDefaultToolkit().getSystemClipboard().setContents(selection, null);
        Messages.showInfoMessage((String)"\u7528\u6237ID\u5df2\u590d\u5236\u5230\u526a\u8d34\u677f", (String)"\u590d\u5236\u6210\u529f");
    }

    private void applyCredentials(String authToken) {
        try {
            CredentialManager credentialManager = new CredentialManager();
            String tenantUrl = "https://bapi.micosoft.icu";
            if (StringUtils.isNotBlank(credentialManager.getAuthToken())) {
                credentialManager.reflectionClear();
            }
            credentialManager.saveCredentials(tenantUrl, authToken);
            credentialManager.reflectionSaveCredentials(tenantUrl, authToken);
            System.out.println("Successfully Applied Credential Settings");
            Messages.showInfoMessage((String)"\u51ed\u636e\u5df2\u81ea\u52a8\u914d\u7f6e", (String)"\u914d\u7f6e\u6210\u529f");
        }
        catch (Exception e) {
            System.out.println("Failed to Apply Credentials: " + e.getMessage());
            Messages.showErrorDialog((String)("\u81ea\u52a8\u914d\u7f6e\u51ed\u636e\u5931\u8d25: " + e.getMessage()), (String)"\u914d\u7f6e\u5931\u8d25");
        }
    }

    private void handleLogout(ActionEvent e) {
        int result = Messages.showYesNoDialog((String)"\u786e\u5b9a\u8981\u9000\u51fa\u767b\u5f55\u5417\uff1f", (String)"\u786e\u8ba4\u9000\u51fa", (Icon)Messages.getQuestionIcon());
        if (result != 0) return;
        this.settings.clearUserData();
        this.activationCodeField.setText("");
        this.updateUI();
        this.updateStatusBar();
        Messages.showInfoMessage((String)"\u5df2\u9000\u51fa\u767b\u5f55", (String)"\u9000\u51fa\u6210\u529f");
    }

    private void updateStatusBar() {
        if (this.project == null) return;
        if (this.project.isDisposed()) return;
        StatusBar statusBar = WindowManager.getInstance().getStatusBar(this.project);
        if (statusBar == null) return;
        statusBar.updateWidget("NumberPoolWidget");
    }

    @Override
    protected Action @NotNull [] createActions() {
        Action[] actionArray = new Action[]{this.getCancelAction()};
        if (actionArray != null) return actionArray;
        NumberPoolDialog.reportNull(0);
        return actionArray;
    }

    private static void reportNull(int n) {
        throw new IllegalStateException(String.format("@NotNull method %s.%s must not return null", "com/aug/augmentplugin/ui/NumberPoolDialog", "createActions"));
    }
}
