package com.aug.augmentplugin.util;

import javax.net.ssl.SSLHandshakeException;
import java.io.IOException;
import java.net.ConnectException;
import java.net.SocketTimeoutException;
import java.net.UnknownHostException;

/**
 * 网络诊断工具类
 * 用于分析和处理网络连接问题
 */
public class NetworkDiagnostics {
    
    /**
     * 分析网络异常并提供用户友好的错误信息
     */
    public static String analyzeNetworkError(Exception e) {
        if (e instanceof SSLHandshakeException) {
            return "SSL连接失败: 可能是证书问题或网络配置问题。请检查网络设置或联系管理员。";
        } else if (e instanceof ConnectException) {
            return "连接被拒绝: 服务器可能未启动或网络不可达。请检查服务器状态和网络连接。";
        } else if (e instanceof SocketTimeoutException) {
            return "连接超时: 网络响应缓慢或服务器负载过高。请稍后重试。";
        } else if (e instanceof UnknownHostException) {
            return "主机名解析失败: 无法解析服务器地址。请检查网络连接和DNS设置。";
        } else if (e instanceof IOException) {
            return "网络IO错误: " + e.getMessage() + "。请检查网络连接。";
        } else {
            return "未知网络错误: " + e.getMessage();
        }
    }
    
    /**
     * 检查是否为可重试的网络错误
     */
    public static boolean isRetryableError(Exception e) {
        return e instanceof SocketTimeoutException || 
               e instanceof ConnectException ||
               (e instanceof IOException && !(e instanceof SSLHandshakeException));
    }
    
    /**
     * 获取网络错误的严重程度
     */
    public static ErrorSeverity getErrorSeverity(Exception e) {
        if (e instanceof SSLHandshakeException) {
            return ErrorSeverity.HIGH;
        } else if (e instanceof UnknownHostException) {
            return ErrorSeverity.HIGH;
        } else if (e instanceof ConnectException) {
            return ErrorSeverity.MEDIUM;
        } else if (e instanceof SocketTimeoutException) {
            return ErrorSeverity.LOW;
        } else {
            return ErrorSeverity.MEDIUM;
        }
    }
    
    public enum ErrorSeverity {
        LOW,    // 可以忽略或稍后重试
        MEDIUM, // 需要用户注意
        HIGH    // 需要立即处理
    }
}
