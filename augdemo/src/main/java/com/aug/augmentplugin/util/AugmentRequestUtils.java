package com.aug.augmentplugin.util;

import com.intellij.openapi.application.ApplicationManager;
import java.lang.reflect.AccessibleObject;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import sun.misc.Unsafe;

public class AugmentRequestUtils {
    private static final String AUGMENT_API_CLASS = "com.augmentcode.intellij.api.AugmentAPI";
    private static final String AUGMENT_API_IMPL_CLASS = "com.augmentcode.intellij.api.AugmentAPIImpl";
    private static final String HTTP_CLIENT_FIELD = "httpClient";
    private static final String SESSION_ID_FIELD = "sessionID";
    public static String originalSessionID;
    public static boolean sessionIDConflict;
    private static Class<?> augmentAPIClass;
    private static Class<?> augmentAPIImplClass;
    private static Field httpClientField;
    private static Field sessionIDField;

    public static String getSessionID() {
        try {
            Object httpClient = AugmentRequestUtils.getHttpClient();
            if (httpClient != null) return (String)sessionIDField.get(httpClient);
            return null;
        }
        catch (Exception e) {
            System.out.println("Get SessionID Failed: " + e.getMessage());
            return null;
        }
    }

    public static boolean setSessionID(String newSessionID) {
        try {
            Object httpClient;
            block5: {
                httpClient = AugmentRequestUtils.getHttpClient();
                if (httpClient == null) {
                    return false;
                }
                try {
                    sessionIDField.set(httpClient, newSessionID);
                }
                catch (IllegalAccessException e) {
                    System.out.println("Failed to Set SessionID Value, Try Alternative Method...");
                    if (AugmentRequestUtils.modifyFinalField(sessionIDField, httpClient, newSessionID)) break block5;
                    System.out.println("Update Final Field Failed");
                    return false;
                }
            }
            String modifiedSessionID = (String)sessionIDField.get(httpClient);
            return newSessionID.equals(modifiedSessionID);
        }
        catch (Exception e) {
            System.out.println("Set SessionID Failed: " + e.getMessage());
            return false;
        }
    }

    private static Object getHttpClient() {
        try {
            AugmentRequestUtils.initReflectionClasses();
            Object augmentAPI = ApplicationManager.getApplication().getService(augmentAPIClass);
            if (augmentAPI == null) {
                System.out.println("Failed to Get AugmentAPI Service Instance");
                return null;
            }
            Object httpClient = httpClientField.get(augmentAPI);
            if (httpClient != null) return httpClient;
            System.out.println("Failed to Get httpClient Instance");
            return null;
        }
        catch (ClassNotFoundException e) {
            System.out.println("Augment Plugin Not Found, Please Install Augment Plugin: " + e.getMessage());
        }
        catch (NoSuchFieldException e) {
            System.out.println("Augment Plugin API Changed, Required Field Not Found: " + e.getMessage());
        }
        catch (Exception e) {
            System.out.println("Get httpClient Instance Failed: " + e.getMessage());
        }
        return null;
    }

    /*
     * Loose catch block
     * Enabled unnecessary exception pruning
     */
    private static boolean modifyFinalField(Field field, Object target, Object newValue) {
        try {
            block10: {
                Method getDeclaredFields0 = Class.class.getDeclaredMethod("getDeclaredFields0", Boolean.TYPE);
                getDeclaredFields0.setAccessible(true);
                Field[] fields = (Field[])getDeclaredFields0.invoke(Field.class, false);
                AccessibleObject modifiersField = null;
                for (Field f : fields) {
                    if (!"modifiers".equals(f.getName())) continue;
                    modifiersField = f;
                    break;
                }
                if (modifiersField == null) break block10;
                modifiersField.setAccessible(true);
                ((Field)modifiersField).setInt(field, field.getModifiers() & 0xFFFFFFEF);
                field.set(target, newValue);
                return true;
                /*{
                    catch (Exception e) {
                    System.out.println("All Methods to Update Final Field Failed: " + e.getMessage());
                    return false;
                    }
                }*/
            }
            try {
                Field unsafeField = Unsafe.class.getDeclaredField("theUnsafe");
                unsafeField.setAccessible(true);
                Unsafe unsafe = (Unsafe)unsafeField.get(null);
                long offset = unsafe.objectFieldOffset(field);
                unsafe.putObject(target, offset, newValue);
                return true;
            }
            catch (Exception ex) {
                System.out.println("Update Final Field Failed via Unsafe: " + ex.getMessage());
                return false;
            }
        }
        catch (NoSuchMethodException exception) {
            System.out.println("Update Final Field Failed via getDeclaredFields0: " + exception.getMessage());
            try {
                Field field1 = Unsafe.class.getDeclaredField("theUnsafe");
                field1.setAccessible(true);
                Unsafe unsafe = (Unsafe)field1.get(null);
                long l = unsafe.objectFieldOffset(field);
                unsafe.putObject(target, l, newValue);
                return true;
            }
            catch (Exception exception1) {
                System.out.println("Update Final Field Failed via Unsafe: " + exception1.getMessage());
                return false;
            }
        } catch (InvocationTargetException e) {
            throw new RuntimeException(e);
        } catch (IllegalAccessException e) {
            throw new RuntimeException(e);
        }
    }

    private static void initReflectionClasses() throws Exception {
        if (augmentAPIClass == null) {
            augmentAPIClass = Class.forName(AUGMENT_API_CLASS);
        }
        if (augmentAPIImplClass == null) {
            augmentAPIImplClass = Class.forName(AUGMENT_API_IMPL_CLASS);
        }
        if (httpClientField == null) {
            httpClientField = augmentAPIImplClass.getDeclaredField(HTTP_CLIENT_FIELD);
            httpClientField.setAccessible(true);
        }
        if (sessionIDField != null) return;
        Object augmentAPI = ApplicationManager.getApplication().getService(augmentAPIClass);
        if (augmentAPI == null) return;
        Object httpClient = httpClientField.get(augmentAPI);
        if (httpClient == null) return;
        sessionIDField = httpClient.getClass().getDeclaredField(SESSION_ID_FIELD);
        sessionIDField.setAccessible(true);
    }

    static {
        sessionIDConflict = false;
    }
}

