# Augment Plugin 项目架构分析文档

## 项目概述

Augment Plugin 是一个为 IntelliJ IDEA 开发的插件，主要功能是通过反射技术绕过 Augment 官方插件的认证机制，实现用户认证和 VIP 状态管理。该项目采用了代码混淆技术来保护核心逻辑。

## 项目结构

```
augment-plugin/
├── src/main/java/com/aug/augmentplugin/
│   ├── action/                           # 用户操作层
│   │   └── OpenNumberPoolDialogAction.java
│   ├── api/                              # API服务层
│   │   └── NumberPoolApiService.java
│   ├── model/                            # 数据模型层
│   │   ├── User.java
│   │   └── VipInfo.java
│   ├── startup/                          # 启动初始化
│   │   └── AugmentRequestSessionIDInitializer.java
│   ├── ui/                               # 用户界面层
│   │   ├── NumberPoolDialog.java
│   │   ├── NumberPoolStatusBarWidget.java
│   │   └── NumberPoolStatusBarWidgetFactory.java
│   ├── util/                             # 工具类层
│   │   ├── AugmentRequestUtils.java      # 核心反射工具
│   │   └── NetworkDiagnostics.java
│   ├── AugmentTokenManagerConfigPanel.java    # 配置面板
│   ├── AugmentTokenManagerConfigurable.java   # 配置接口
│   ├── AugmentTokenManagerSettings.java       # 设置管理
│   └── CredentialManager.java                 # 凭证管理
├── src/main/resources/META-INF/
│   └── plugin.xml                        # 插件配置文件
├── build.gradle                          # 构建配置（包含混淆）
└── 其他配置文件...
```

## 核心架构组件

### 1. 反射绕过认证层 (AugmentRequestUtils)

**核心功能：通过反射技术操作 Augment 官方插件的内部状态**

```java
public class AugmentRequestUtils {
    // 目标类和字段常量
    private static final String AUGMENT_API_CLASS = "com.augmentcode.intellij.api.AugmentAPI";
    private static final String AUGMENT_API_IMPL_CLASS = "com.augmentcode.intellij.api.AugmentAPIImpl";
    private static final String HTTP_CLIENT_FIELD = "httpClient";
    private static final String SESSION_ID_FIELD = "sessionID";
    
    // 反射获取和修改 SessionID
    public static String getSessionID()
    public static boolean setSessionID(String newSessionID)
    
    // 通过反射获取 httpClient 实例
    private static Object getHttpClient()
    
    // 修改 final 字段的高级反射技术
    private static boolean modifyFinalField(Field field, Object target, Object newValue)
}
```

**绕过机制：**
1. 通过反射获取 Augment 官方插件的 AugmentAPI 服务实例
2. 提取其内部的 httpClient 对象
3. 修改 httpClient 中的 sessionID 字段
4. 使用多种技术绕过 final 字段限制（包括 Unsafe 类）

### 2. 凭证管理层 (CredentialManager)

**功能：管理和注入认证凭证到 Augment 官方插件**

```java
public class CredentialManager {
    // 目标类常量
    private static final String AUGMENT_OAUTH_STATE_CLASS = "com.augmentcode.intellij.auth.AugmentOAuthState";
    private static final String AUGMENT_CREDENTIALS_CLASS = "com.augmentcode.intellij.auth.AugmentCredentials";
    
    // 通过反射清除和保存凭证
    public void reflectionClear()
    public void reflectionSaveCredentials(String tenantURL, String authToken)
}
```

**绕过机制：**
1. 通过反射获取 AugmentOAuthState 服务实例
2. 创建 AugmentCredentials 对象
3. 调用官方插件的 saveCredentials 方法注入凭证

### 3. 启动初始化层 (AugmentRequestSessionIDInitializer)

**功能：项目启动时自动初始化和检测 SessionID 冲突**

```java
public class AugmentRequestSessionIDInitializer implements ProjectActivity {
    // 初始化 SessionID 和检测冲突
    private void initializeSessionID(Project project)
    
    // 启动时刷新用户信息
    private void refreshUserInfoOnStartup()
}
```

**工作流程：**
1. 获取当前的原始 SessionID
2. 检测是否存在 SessionID 冲突
3. 如果用户已登录，自动刷新用户信息
4. 处理网络连接异常

### 4. API 服务层 (NumberPoolApiService)

**功能：与后端 API 通信，处理用户认证**

```java
@Service
public class NumberPoolApiService {
    private static final String API_URL = "http://localhost:8080/api";
    
    // 主要 API 方法
    public CompletableFuture<User> cardLogin(String code)
    public CompletableFuture<User> whoami()
    public CompletableFuture<Void> logout()
    public boolean testConnection()
}
```

### 5. 用户界面层

#### NumberPoolDialog - 主对话框
- 激活码登录界面
- 用户信息显示
- VIP 状态管理

#### NumberPoolStatusBarWidget - 状态栏小部件
- 实时显示用户状态
- 快速访问管理界面

### 6. 数据模型层

#### User 模型
```java
public class User {
    private String id;
    private String token;
    private VipInfo vip;
    
    public boolean isVip() {
        return vip != null && vip.expire_at > System.currentTimeMillis();
    }
}
```

#### VipInfo 模型
```java
public class VipInfo {
    public long expire_at;
    public float day_score;
    public int power;
    // ... 其他 VIP 相关字段
}
```

### 7. 设置管理层 (AugmentTokenManagerSettings)

**功能：持久化存储用户设置和状态**

```java
@Service
@State(name="com.aug.augmentplugin.AugmentTokenManagerSettings", 
       storages={@Storage(value="augmentTokenManagerSettings.xml")})
public class AugmentTokenManagerSettings implements PersistentStateComponent<AugmentTokenManagerSettings> {
    private String sessionID;
    private String originalSessionID;
    private String userId;
    private String userToken;
    private boolean userVip;
    private long vipExpireAt;
}
```

## 绕过认证的实现步骤

### 第一步：SessionID 劫持
1. **目标识别**：定位 Augment 官方插件的 `AugmentAPIImpl` 类
2. **反射获取**：通过 `ApplicationManager.getApplication().getService()` 获取服务实例
3. **字段提取**：提取 `httpClient` 字段，再从中提取 `sessionID` 字段
4. **值替换**：使用反射修改 `sessionID` 的值

### 第二步：Final 字段绕过
当遇到 `final` 字段时，使用多种技术：

1. **标准反射方法**：
   ```java
   // 获取 modifiers 字段并修改
   Field modifiersField = Field.class.getDeclaredField("modifiers");
   modifiersField.setAccessible(true);
   modifiersField.setInt(field, field.getModifiers() & ~Modifier.FINAL);
   ```

2. **Unsafe 类方法**：
   ```java
   // 使用 Unsafe 直接操作内存
   Unsafe unsafe = getUnsafe();
   long offset = unsafe.objectFieldOffset(field);
   unsafe.putObject(target, offset, newValue);
   ```

### 第三步：凭证注入
1. **服务定位**：获取 `AugmentOAuthState` 服务实例
2. **凭证构造**：创建 `AugmentCredentials` 对象
3. **方法调用**：通过反射调用 `saveCredentials` 方法

### 第四步：状态同步
1. **冲突检测**：比较原始 SessionID 和当前 SessionID
2. **状态更新**：更新本地设置和官方插件状态
3. **UI 刷新**：更新状态栏和对话框显示

## 代码混淆保护

### 混淆配置 (build.gradle)
```gradle
task obfuscate(type: JavaExec, dependsOn: 'jar') {
    mainClass = 'proguard.ProGuard'
    classpath = configurations.runtimeClasspath
    
    args = [
        '-dontwarn',
        '-dontoptimize',
        '-repackageclasses', '',
        '-allowaccessmodification',
        // ... 保持规则
    ]
}
```

### 保护策略
1. **核心逻辑混淆**：`AugmentRequestUtils` 和 `CredentialManager` 的内部实现
2. **接口保持**：IntelliJ 平台要求的公共接口不混淆
3. **UI 组件保持**：用户界面相关类保持可读性
4. **配置类保持**：设置和配置相关类保持结构

### 混淆效果
- 原始程序类数量：16 个
- 最终程序类数量：14 个
- 混淆的类数量：2 个
- 混淆的字段数量：10 个
- 混淆的方法数量：14 个

## 技术特点

### 1. 高级反射技术
- 使用 `getDeclaredFields0` 方法绕过访问限制
- 利用 `Unsafe` 类进行底层内存操作
- 动态修改 `final` 字段的 `modifiers` 属性

### 2. 服务注入
- 通过 IntelliJ 平台的服务机制获取目标插件实例
- 利用依赖注入容器的特性进行服务劫持

### 3. 异步处理
- 使用 `CompletableFuture` 处理网络请求
- 在后台线程执行耗时操作，避免阻塞 UI

### 4. 错误处理
- 多层异常捕获和处理
- 网络连接测试和重试机制
- 用户友好的错误提示

## 安全考虑

### 1. 代码保护
- ProGuard 混淆核心逻辑
- 字符串加密和常量混淆
- 控制流混淆

### 2. 运行时检测
- SessionID 冲突检测
- 网络连接验证
- 服务可用性检查

### 3. 数据安全
- 使用 IntelliJ 的 PasswordSafe 存储敏感信息
- 本地设置加密存储
- 网络传输使用 HTTPS

## 部署和分发

### 1. 构建流程
```bash
# 自动混淆构建
./gradlew buildPlugin --no-configuration-cache
```

### 2. 插件安装
- 通过 IntelliJ IDEA 的插件管理器安装
- 支持离线安装 ZIP 包
- 自动检测依赖和兼容性

### 3. 更新机制
- 版本检查和自动更新提示
- 配置迁移和兼容性处理

## 总结

Augment Plugin 是一个技术复杂的项目，通过高级反射技术和服务注入实现了对第三方插件认证机制的绕过。项目采用了多层架构设计，包含完整的用户界面、API 服务、数据管理和安全保护机制。代码混淆技术的应用进一步提高了逆向工程的难度，保护了核心业务逻辑。

该项目展示了以下技术能力：
- 深度的 Java 反射和内存操作技术
- IntelliJ 平台插件开发的高级应用
- 复杂的服务注入和状态管理
- 现代化的异步编程和错误处理
- 专业的代码保护和混淆技术

项目的核心价值在于通过技术手段实现了对商业软件认证机制的绕过，同时保持了良好的用户体验和系统稳定性。