package ai.zencoder.plugin.ui;

import ai.zencoder.plugin.noauth.AuthServiceReplacer;

import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.wm.ToolWindow;
import com.intellij.openapi.wm.ToolWindowManager;
import org.jetbrains.annotations.NotNull;

import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * 自动登录绕过器
 * 自动检测登录界面并执行绕过操作
 */
public class AutoLoginBypass {
    
    private static final Logger LOG = Logger.getInstance(AutoLoginBypass.class);
    
    private static volatile boolean autoBypassEnabled = true;
    private static volatile boolean bypassInProgress = false;
    
    // 可能的登录相关工具窗口ID
    private static final String[] LOGIN_WINDOW_IDS = {
        "Zencoder Login",
        "Zencoder Auth", 
        "Zencoder Authentication",
        "Login",
        "Authentication"
    };
    
    // 可能的登录相关组件文本
    private static final String[] LOGIN_INDICATORS = {
        "login", "sign in", "authenticate", "log in",
        "登录", "认证", "身份验证"
    };
    
    /**
     * 启动自动登录绕过监控
     */
    public static void startAutoBypassMonitoring(@NotNull Project project) {
        if (!autoBypassEnabled) {
            LOG.debug("Auto bypass is disabled");
            return;
        }
        
        LOG.info("Starting auto login bypass monitoring...");
        
        // 异步监控以避免阻塞
        CompletableFuture.runAsync(() -> {
            try {
                monitorForLoginUI(project);
            } catch (Exception e) {
                LOG.error("Auto bypass monitoring failed", e);
            }
        });
    }
    
    /**
     * 监控登录界面
     */
    private static void monitorForLoginUI(@NotNull Project project) {
        int checkCount = 0;
        final int maxChecks = 60; // 监控60次，每次间隔1秒，总共1分钟
        
        while (checkCount < maxChecks && autoBypassEnabled) {
            try {
                if (detectLoginUI(project)) {
                    LOG.info("Login UI detected, attempting auto bypass...");
                    performAutoBypass(project);
                    break; // 执行一次绕过后退出监控
                }
                
                checkCount++;
                Thread.sleep(1000); // 每秒检查一次
                
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                LOG.debug("Auto bypass monitoring interrupted");
                break;
            } catch (Exception e) {
                LOG.debug("Error during login UI monitoring", e);
            }
        }
        
        LOG.debug("Auto bypass monitoring completed after " + checkCount + " checks");
    }
    
    /**
     * 检测是否存在登录界面
     */
    private static boolean detectLoginUI(@NotNull Project project) {
        try {
            // 检查工具窗口
            if (detectLoginToolWindows(project)) {
                return true;
            }
            
            // 检查主界面组件
            if (detectLoginComponents()) {
                return true;
            }
            
            return false;
            
        } catch (Exception e) {
            LOG.debug("Failed to detect login UI", e);
            return false;
        }
    }
    
    /**
     * 检测登录相关的工具窗口
     */
    private static boolean detectLoginToolWindows(@NotNull Project project) {
        try {
            ToolWindowManager toolWindowManager = ToolWindowManager.getInstance(project);
            
            for (String windowId : LOGIN_WINDOW_IDS) {
                ToolWindow toolWindow = toolWindowManager.getToolWindow(windowId);
                if (toolWindow != null && toolWindow.isVisible()) {
                    LOG.debug("Detected login tool window: " + windowId);
                    return true;
                }
            }
            
            return false;
            
        } catch (Exception e) {
            LOG.debug("Failed to detect login tool windows", e);
            return false;
        }
    }
    
    /**
     * 检测登录相关的UI组件
     */
    private static boolean detectLoginComponents() {
        try {
            // 获取所有顶级窗口
            Window[] windows = Window.getWindows();
            
            for (Window window : windows) {
                if (window.isVisible() && containsLoginIndicators(window)) {
                    LOG.debug("Detected login UI in window: " + window.getClass().getSimpleName());
                    return true;
                }
            }
            
            return false;
            
        } catch (Exception e) {
            LOG.debug("Failed to detect login components", e);
            return false;
        }
    }
    
    /**
     * 检查窗口是否包含登录指示器
     */
    private static boolean containsLoginIndicators(@NotNull Window window) {
        try {
            return checkComponentsRecursively(window);
        } catch (Exception e) {
            LOG.debug("Failed to check login indicators", e);
            return false;
        }
    }
    
    /**
     * 递归检查组件
     */
    private static boolean checkComponentsRecursively(@NotNull Container container) {
        try {
            Component[] components = container.getComponents();
            
            for (Component component : components) {
                // 检查文本组件
                if (component instanceof JLabel) {
                    String text = ((JLabel) component).getText();
                    if (text != null && containsLoginKeywords(text.toLowerCase())) {
                        return true;
                    }
                } else if (component instanceof JButton) {
                    String text = ((JButton) component).getText();
                    if (text != null && containsLoginKeywords(text.toLowerCase())) {
                        return true;
                    }
                } else if (component instanceof Container) {
                    if (checkComponentsRecursively((Container) component)) {
                        return true;
                    }
                }
            }
            
            return false;
            
        } catch (Exception e) {
            LOG.debug("Failed to check components recursively", e);
            return false;
        }
    }
    
    /**
     * 检查文本是否包含登录关键词
     */
    private static boolean containsLoginKeywords(@NotNull String text) {
        for (String indicator : LOGIN_INDICATORS) {
            if (text.contains(indicator)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 执行自动绕过
     */
    private static void performAutoBypass(@NotNull Project project) {
        if (bypassInProgress) {
            LOG.debug("Auto bypass already in progress");
            return;
        }
        
        bypassInProgress = true;
        
        try {
            LOG.info("Performing automatic login bypass...");
            
            // 步骤1：确保绕过系统激活
            if (!ensureBypassSystemActive(project)) {
                LOG.error("Failed to ensure bypass system is active");
                return;
            }
            
            // 步骤2：关闭登录界面
            closeLoginUI(project);
            
            // 步骤3：激活主界面
            activateMainUI(project);
            
            // 步骤4：验证绕过成功
            verifyBypassSuccess(project);
            
            LOG.info("Automatic login bypass completed successfully");
            
        } catch (Exception e) {
            LOG.error("Failed to perform auto bypass", e);
        } finally {
            bypassInProgress = false;
        }
    }
    
    /**
     * 确保绕过系统激活
     */
    private static boolean ensureBypassSystemActive(@NotNull Project project) {
        try {
            // 检查 Legacy 系统
            AuthServiceReplacer replacer = ApplicationManager.getApplication().getService(AuthServiceReplacer.class);
            if (replacer == null || !replacer.isNoAuthActive()) {
                LOG.warn("Legacy no-auth system not active, attempting to activate...");
                // 这里可以尝试重新激活
            }
            
            // 简化的绕过系统检查
            LOG.info("Bypass system check completed for auto login...");
            
            return true;
            
        } catch (Exception e) {
            LOG.error("Failed to ensure bypass system active", e);
            return false;
        }
    }
    
    /**
     * 关闭登录界面
     */
    private static void closeLoginUI(@NotNull Project project) {
        try {
            LOG.debug("Attempting to close login UI...");
            
            // 关闭登录相关的工具窗口
            ToolWindowManager toolWindowManager = ToolWindowManager.getInstance(project);
            for (String windowId : LOGIN_WINDOW_IDS) {
                ToolWindow toolWindow = toolWindowManager.getToolWindow(windowId);
                if (toolWindow != null && toolWindow.isVisible()) {
                    toolWindow.hide(null);
                    LOG.debug("Closed login tool window: " + windowId);
                }
            }
            
            // 关闭登录对话框
            closeLoginDialogs();
            
        } catch (Exception e) {
            LOG.debug("Failed to close login UI", e);
        }
    }
    
    /**
     * 关闭登录对话框
     */
    private static void closeLoginDialogs() {
        try {
            Window[] windows = Window.getWindows();
            
            for (Window window : windows) {
                if (window.isVisible() && containsLoginIndicators(window)) {
                    if (window instanceof Dialog) {
                        ((Dialog) window).setVisible(false);
                        LOG.debug("Closed login dialog");
                    } else if (window instanceof Frame) {
                        // 不关闭主框架，只是隐藏登录相关内容
                        LOG.debug("Found login frame, but not closing main frame");
                    }
                }
            }
            
        } catch (Exception e) {
            LOG.debug("Failed to close login dialogs", e);
        }
    }
    
    /**
     * 激活主界面
     */
    private static void activateMainUI(@NotNull Project project) {
        try {
            LOG.debug("Activating main UI...");
            
            // 激活 Zencoder 相关的工具窗口
            ZencoderUIActivator.activateZencoderUI(project);
            
            // 确保安全聊天窗口可见
            ToolWindowManager toolWindowManager = ToolWindowManager.getInstance(project);
            ToolWindow safeChat = toolWindowManager.getToolWindow("ZencoderSafeChat");
            if (safeChat != null) {
                safeChat.activate(null);
                LOG.debug("Activated ZencoderSafeChat window");
            }
            
        } catch (Exception e) {
            LOG.debug("Failed to activate main UI", e);
        }
    }
    
    /**
     * 验证绕过成功
     */
    private static void verifyBypassSuccess(@NotNull Project project) {
        try {
            LOG.debug("Verifying bypass success...");
            
            // 检查是否还有登录界面
            if (!detectLoginUI(project)) {
                LOG.info("✅ Auto bypass verification: No login UI detected");
            } else {
                LOG.warn("⚠️ Auto bypass verification: Login UI still present");
            }
            
            // 检查绕过系统状态
            AuthServiceReplacer replacer = ApplicationManager.getApplication().getService(AuthServiceReplacer.class);
            if (replacer != null && replacer.isNoAuthActive()) {
                LOG.info("✅ Auto bypass verification: No-auth system active");
            } else {
                LOG.warn("⚠️ Auto bypass verification: No-auth system inactive");
            }
            
        } catch (Exception e) {
            LOG.debug("Failed to verify bypass success", e);
        }
    }
    
    /**
     * 启用/禁用自动绕过
     */
    public static void setAutoBypassEnabled(boolean enabled) {
        autoBypassEnabled = enabled;
        LOG.info("Auto login bypass " + (enabled ? "enabled" : "disabled"));
    }
    
    /**
     * 检查自动绕过是否启用
     */
    public static boolean isAutoBypassEnabled() {
        return autoBypassEnabled;
    }
    
    /**
     * 检查是否正在执行绕过
     */
    public static boolean isBypassInProgress() {
        return bypassInProgress;
    }
}
