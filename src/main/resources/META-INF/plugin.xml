<?xml version="1.0" encoding="UTF-8"?>
<idea-plugin>
    <id>ai.zencoder.plugin.noauth</id>
    <name>Zencoder No-Auth Extension</name>
    <version>1.0.0</version>
    <vendor email="<EMAIL>" url="https://your-website.com">Your Company</vendor>
    
    <description><![CDATA[
    <h1>Zencoder No-Auth Extension</h1>
    <p>Simplified plugin that automatically bypasses Zencoder authentication for development environments.</p>

    <h2>Features</h2>
    <ul>
        <li>Automatic authentication bypass</li>
        <li>Mock user credentials</li>
        <li>Development environment only</li>
    </ul>

    <h2>Usage</h2>
    <p>Install alongside Zencoder. Authentication is automatically bypassed on startup.</p>
    ]]></description>
    
    <!-- Plugin dependencies -->
    <depends optional="false">ai.zencoder.plugin</depends>
    
    <!-- Compatible IDE versions -->
    <idea-version since-build="233" until-build="243.*"/>
    
    <!-- Extension points -->
    <extensions defaultExtensionNs="com.intellij">
        <!-- AuthObserver implementation -->
        <applicationService
            serviceInterface="ai.zencoder.plugin.observers.auth.AuthObserver"
            serviceImplementation="ai.zencoder.plugin.observers.auth.AuthObserverImpl"/>

        <!-- Configuration manager -->
        <applicationService
            serviceImplementation="ai.zencoder.plugin.noauth.SecureNoAuthConfigManager"/>

        <!-- AuthService replacer -->
        <applicationService
            serviceImplementation="ai.zencoder.plugin.noauth.AuthServiceReplacer"/>

        <!-- Startup activity -->
        <projectActivity implementation="ai.zencoder.plugin.startup.NoAuthStartupActivity"/>

        <!-- Notification group -->
        <notificationGroup id="Zencoder.NoAuth"
                          displayType="BALLOON"
                          key="notification.group.zencoder.noauth"/>
    </extensions>
</idea-plugin>
